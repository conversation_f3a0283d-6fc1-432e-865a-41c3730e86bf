<template>
  <div class="min-h-screen bg-gradient-to-b from-blue-50 to-white flex items-center justify-center">
    <!-- Main Content -->
    <main class="w-full px-4 flex flex-col items-center">
      <div class="max-w-md w-full">
        <!-- 登录卡片 -->
        <div class="bg-white/80 backdrop-blur-sm py-8 px-4 shadow-lg rounded-lg sm:px-10">
          <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900">Welcome to Fillify</h2>
            <p class="mt-2 text-sm text-gray-600">
              Sign in to use our AI-powered form filling extension
            </p>
          </div>

          <!-- Email Login Section -->
          <div class="mb-6">
            <!-- Email Input -->
            <div v-if="!otpSent">
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                id="email"
                v-model="email"
                type="email"
                required
                :disabled="isLoading"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                placeholder="Enter your email address"
              />
              <button
                @click="sendOTP"
                :disabled="isLoading || !email || !isValidEmail"
                class="w-full mt-3 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isLoading" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending...
                </span>
                <span v-else>Continue with email</span>
              </button>
            </div>

            <!-- OTP Input -->
            <div v-else>
              <div class="text-center mb-4">
                <p class="text-sm text-gray-600">
                  We've sent a verification code to
                </p>
                <p class="text-sm font-medium text-gray-900">{{ email }}</p>
              </div>

              <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
                Login Code
              </label>
              <input
                id="otp"
                v-model="otp"
                type="text"
                required
                maxlength="6"
                :disabled="isLoading"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 text-center text-lg tracking-widest"
                placeholder="Enter verification code"
              />

              <button
                @click="verifyOTP"
                :disabled="isLoading || !otp || otp.length !== 6"
                class="w-full mt-3 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isLoading" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Verifying...
                </span>
                <span v-else>Continue with login code</span>
              </button>

              <!-- Resend Code -->
              <div class="text-center mt-3">
                <button
                  @click="resendOTP"
                  :disabled="isResending || resendCooldown > 0"
                  class="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span v-if="isResending" class="flex items-center justify-center">
                    <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-1"></div>
                    Resending...
                  </span>
                  <span v-else-if="resendCooldown > 0">
                    Resend in {{ resendCooldown }}s
                  </span>
                  <span v-else>Resend code</span>
                </button>
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="errorMessage" class="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
              <p class="text-sm text-red-600">{{ errorMessage }}</p>
            </div>
          </div>

          <!-- Divider and Google Login (only show when not in OTP verification mode) -->
          <div v-if="!otpSent">
            <!-- Divider -->
            <div class="relative mb-6">
              <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-300" />
              </div>
              <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">or</span>
              </div>
            </div>

            <!-- Google Sign In Button -->
            <div class="mb-6">
              <div
                id="googleSignInButton"
                class="flex justify-center w-full max-w-[320px] mx-auto"
                :class="{ 'opacity-50 pointer-events-none': isGoogleLoading }"
              ></div>
            </div>
          </div>

          <!-- Features -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-sm font-medium text-gray-500 mb-4">What you'll get:</h3>
            <ul class="space-y-4">
              <li class="flex items-center text-sm text-gray-600">
                <Sparkles class="h-5 w-5 text-blue-500 mr-3" />
                AI-Powered Form Auto-Fill
              </li>
              <li class="flex items-center text-sm text-gray-600">
                <Wand class="h-5 w-5 text-purple-500 mr-3" />
                Customize with Your Own API
              </li>
              <li class="flex items-center text-sm text-gray-600">
                <Rocket class="h-5 w-5 text-orange-500 mr-3" />
                Early Access to New Features
              </li>
            </ul>
          </div>
        </div>

        <!-- Terms and Privacy -->
        <div class="mt-6 text-center text-xs text-gray-500">
          <span class="block">By signing in, you agree to our</span>
          <span class="mt-1 inline-block">
            <NuxtLink to="/terms" class="text-blue-600 hover:text-blue-800">
              Terms of Service
            </NuxtLink>
            <span class="mx-1">and</span>
            <NuxtLink to="/privacy" class="text-blue-600 hover:text-blue-800">
              Privacy Policy
            </NuxtLink>
          </span>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, inject, watch, computed } from 'vue'
import { cookieUtil } from '~/utils/cookie'
import { Sparkles, Wand, Rocket } from 'lucide-vue-next'
import { useAuth } from '~/composables/useAuth'

// 类型定义
interface UserState {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

// 声明 google 全局变量
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void
          renderButton: (element: HTMLElement | null, options: any) => void
          disableAutoSelect: () => void
          revoke: (email: string, callback: () => void) => void
        }
      }
    }
  }
}

const { locale } = useI18n()
const router = useRouter()
const { user, isLoggedIn, fetchUserData } = useAuth()
const isGoogleScriptLoaded = ref(false)
const isLoading = ref(false)
const config = useRuntimeConfig()
const googleClientId = config.public.GOOGLE_CLIENT_ID

// 获取类型安全的 userState
const userState = inject<UserState>('userState')

// 邮箱验证码登录相关状态
const email = ref('')
const otp = ref('')
const otpSent = ref(false)
const errorMessage = ref('')
const resendCooldown = ref(0)
const isResending = ref(false)
const isGoogleLoading = ref(false)

// 邮箱格式验证
const isValidEmail = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.value)
})

// 确保在使用前检查 googleClientId
if (!googleClientId && process.dev) {
  console.error('Google Client ID is not configured')
}

// 监听登录状态
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    const currentLang = locale.value
    const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
    navigateTo(dashboardPath)
  }
}, { immediate: true })

// 发送验证码
const sendOTP = async () => {
  if (!email.value || !isValidEmail.value) {
    errorMessage.value = 'Please enter a valid email address'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/send-otp', {
      method: 'POST',
      body: {
        email: email.value
      }
    })

    if (response.success) {
      otpSent.value = true
      startResendCooldown()
    }
  } catch (error: any) {
    console.error('Send OTP error:', error)
    errorMessage.value = error.data?.message || 'Failed to send verification code. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// 重发验证码（单独的函数，不影响主按钮的加载状态）
const resendOTP = async () => {
  if (!email.value || !isValidEmail.value) {
    errorMessage.value = 'Please enter a valid email address'
    return
  }

  isResending.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/send-otp', {
      method: 'POST',
      body: {
        email: email.value
      }
    })

    if (response.success) {
      startResendCooldown()
    }
  } catch (error: any) {
    console.error('Resend OTP error:', error)
    errorMessage.value = error.data?.message || 'Failed to resend verification code. Please try again.'
  } finally {
    isResending.value = false
  }
}

// 验证验证码并登录
const verifyOTP = async () => {
  if (!otp.value || otp.value.length !== 6) {
    errorMessage.value = 'Please enter a valid 6-digit verification code'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/verify-otp', {
      method: 'POST',
      body: {
        email: email.value,
        token: otp.value
      }
    })

    if (response.success && response.user) {
      // 设置用户状态
      user.value = response.user
      cookieUtil.setCookie('xToken', response.user.id, {
        expires: 7,
        path: '/'
      })

      // 更新全局状态
      if (userState) {
        userState.isLoggedIn = true
        userState.credits = response.user.credits || 3
        userState.userId = response.user.id
        localStorage.setItem('userState', JSON.stringify(userState))
      }

      // 等待状态更新完成
      await nextTick()
      // 强制刷新用户数据
      await fetchUserData()

      // 导航到仪表板
      try {
        const currentLang = locale.value
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        await navigateTo(dashboardPath, { replace: true })
      } catch (navError) {
        console.error('Navigation error:', navError)
        window.location.href = localePath('/dashboard')
      }
    }
  } catch (error: any) {
    console.error('Verify OTP error:', error)
    errorMessage.value = error.data?.message || 'Invalid or expired verification code. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// 重置验证码状态
const resetOTP = () => {
  otpSent.value = false
  otp.value = ''
  errorMessage.value = ''
  resendCooldown.value = 0
}

// 开始重发倒计时
const startResendCooldown = () => {
  resendCooldown.value = 60
  const timer = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 处理Google登录回调
const handleCredentialResponse = async (response: any) => {
  console.log("Encoded JWT ID token: " + response.credential)
  isGoogleLoading.value = true
  errorMessage.value = ''

  try {
    const res = await fetch(`${config.public.apiBase}/api/auth/google-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ credential: response.credential }),
    })
    const data = await res.json()

    if (data.success) {
      user.value = data.user
      cookieUtil.setCookie('xToken', data.user.id, {
        expires: 7,
        path: '/'
      })

      // 更新全局状态
      if (userState) {
        userState.isLoggedIn = true
        userState.credits = data.user.credits || 3
        userState.userId = data.user.id
        localStorage.setItem('userState', JSON.stringify(userState))
      }

      // 等待状态更新完成
      await nextTick()
      // 强制刷新用户数据
      await fetchUserData()

      // 使用 try-catch 确保导航成功
      try {
        const currentLang = locale.value
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        await navigateTo(dashboardPath, { replace: true })
      } catch (navError) {
        console.error('Navigation error:', navError)
        // 如果导航失败，使用 window.location
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        window.location.href = dashboardPath
      }
    }
  } catch (error) {
    console.error('Google login error:', error)
    errorMessage.value = 'Google login failed. Please try again.'
  } finally {
    isGoogleLoading.value = false
  }
}

// 确保在使用前检查 googleClientId
if (!googleClientId && process.dev) {
  console.error('Google Client ID is not configured')
}

// 加载Google登录按钮
onMounted(() => {
  if (googleClientId) {
    const script = document.createElement('script')
    script.src = 'https://accounts.google.com/gsi/client'
    script.onload = () => {
      if (window.google?.accounts?.id) {
        window.google.accounts.id.initialize({
          client_id: googleClientId,
          callback: handleCredentialResponse,
        })
        const buttonElement = document.getElementById('googleSignInButton')
        if (buttonElement) {
          window.google.accounts.id.renderButton(
            buttonElement,
            { theme: 'outline', size: 'large', width: 320 }
          )
        }
        isGoogleScriptLoaded.value = true
      }
    }
    document.head.appendChild(script)
  }
})

// SEO
useHead({
  title: 'Sign In - Fillify',
  meta: [
    { name: 'description', content: 'Sign in to Fillify to access your AI-powered form filling assistant' },
  ],
})
</script>