<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <div class="flex justify-center mb-6">
          <div class="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
            <Sparkles class="w-8 h-8 text-white" />
          </div>
        </div>
        <h2 class="text-3xl font-bold text-gray-900 mb-2">Welcome to Fillify</h2>
        <p class="text-gray-600">Sign in to access your AI form filling assistant</p>
      </div>

      <div class="bg-white rounded-xl shadow-lg p-8 space-y-6">
        <!-- Features Preview -->
        <div class="space-y-4 mb-6">
          <div class="flex items-center space-x-3 text-sm text-gray-600">
            <Wand class="w-4 h-4 text-blue-500" />
            <span>AI-powered form filling</span>
          </div>
          <div class="flex items-center space-x-3 text-sm text-gray-600">
            <Rocket class="w-4 h-4 text-blue-500" />
            <span>Save time with smart automation</span>
          </div>
        </div>

        <!-- Login Methods Tabs -->
        <div class="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
          <button
            @click="loginMethod = 'email'"
            :class="[
              'flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors',
              loginMethod === 'email'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            ]"
          >
            Email Code
          </button>
          <button
            @click="loginMethod = 'google'"
            :class="[
              'flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors',
              loginMethod === 'google'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            ]"
          >
            Google
          </button>
        </div>

        <!-- Email Code Login -->
        <div v-if="loginMethod === 'email'" class="space-y-4">
          <!-- Email Input -->
          <div v-if="!otpSent">
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              id="email"
              v-model="email"
              type="email"
              required
              :disabled="isLoading"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
              placeholder="Enter your email address"
            />
            <button
              @click="sendOTP"
              :disabled="isLoading || !email || !isValidEmail"
              class="w-full mt-4 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <span v-if="isLoading" class="flex items-center justify-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sending...
              </span>
              <span v-else>Send Verification Code</span>
            </button>
          </div>

          <!-- OTP Input -->
          <div v-else>
            <div class="text-center mb-4">
              <p class="text-sm text-gray-600">
                We've sent a verification code to
              </p>
              <p class="text-sm font-medium text-gray-900">{{ email }}</p>
            </div>

            <label for="otp" class="block text-sm font-medium text-gray-700 mb-2">
              Verification Code
            </label>
            <input
              id="otp"
              v-model="otp"
              type="text"
              required
              maxlength="6"
              :disabled="isLoading"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 text-center text-lg tracking-widest"
              placeholder="000000"
            />

            <div class="flex space-x-3 mt-4">
              <button
                @click="verifyOTP"
                :disabled="isLoading || !otp || otp.length !== 6"
                class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span v-if="isLoading" class="flex items-center justify-center">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Verifying...
                </span>
                <span v-else>Verify & Sign In</span>
              </button>

              <button
                @click="resetOTP"
                :disabled="isLoading"
                class="px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors disabled:opacity-50"
              >
                Change Email
              </button>
            </div>

            <!-- Resend Code -->
            <div class="text-center mt-4">
              <button
                @click="sendOTP"
                :disabled="isLoading || resendCooldown > 0"
                class="text-sm text-blue-600 hover:text-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="resendCooldown > 0">
                  Resend code in {{ resendCooldown }}s
                </span>
                <span v-else>Resend verification code</span>
              </button>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="errorMessage" class="p-3 bg-red-50 border border-red-200 rounded-md">
            <p class="text-sm text-red-600">{{ errorMessage }}</p>
          </div>
        </div>

        <!-- Google Sign In -->
        <div v-else-if="loginMethod === 'google'" class="space-y-4">
          <div
            id="googleSignInButton"
            class="flex justify-center"
            :class="{ 'opacity-50 pointer-events-none': isLoading }"
          ></div>

          <div v-if="isLoading" class="flex justify-center">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        </div>

        <!-- Terms and Privacy -->
        <div class="text-xs text-gray-500 text-center space-y-1">
          <p>By signing in, you agree to our</p>
          <div class="space-x-2">
            <NuxtLink :to="localePath('/terms')" class="text-blue-600 hover:underline">
              Terms of Service
            </NuxtLink>
            <span>and</span>
            <NuxtLink :to="localePath('/privacy')" class="text-blue-600 hover:underline">
              Privacy Policy
            </NuxtLink>
          </div>
        </div>
      </div>

      <!-- Back to Home -->
      <div class="text-center">
        <NuxtLink
          :to="localePath('/')"
          class="text-sm text-gray-600 hover:text-gray-900 transition-colors"
        >
          ← Back to Home
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, inject, watch, computed } from 'vue'
import { cookieUtil } from '~/utils/cookie'
import { Sparkles, Wand, Rocket } from 'lucide-vue-next'
import { useAuth } from '~/composables/useAuth'

// 类型定义
interface UserState {
  isLoggedIn: boolean
  credits: number
  userId: string | null
}

// 声明 google 全局变量
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void
          renderButton: (element: HTMLElement | null, options: any) => void
          disableAutoSelect: () => void
          revoke: (email: string, callback: () => void) => void
        }
      }
    }
  }
}

const { locale } = useI18n()
const router = useRouter()
const localePath = useLocalePath()
const { user, isLoggedIn, fetchUserData } = useAuth()
const isGoogleScriptLoaded = ref(false)
const isLoading = ref(false)
const config = useRuntimeConfig()
const googleClientId = config.public.GOOGLE_CLIENT_ID
const currentLocale = computed(() => locale.value)

// 获取类型安全的 userState
const userState = inject<UserState>('userState')

// 邮箱验证码登录相关状态
const loginMethod = ref<'email' | 'google'>('email')
const email = ref('')
const otp = ref('')
const otpSent = ref(false)
const errorMessage = ref('')
const resendCooldown = ref(0)

// 邮箱格式验证
const isValidEmail = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.value)
})

// 监听登录状态
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    navigateTo(localePath('/dashboard'))
  }
}, { immediate: true })

// 发送验证码
const sendOTP = async () => {
  if (!email.value || !isValidEmail.value) {
    errorMessage.value = 'Please enter a valid email address'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/send-otp', {
      method: 'POST',
      body: {
        email: email.value
      }
    })

    if (response.success) {
      otpSent.value = true
      startResendCooldown()
    }
  } catch (error: any) {
    console.error('Send OTP error:', error)
    errorMessage.value = error.data?.message || 'Failed to send verification code. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// 验证验证码并登录
const verifyOTP = async () => {
  if (!otp.value || otp.value.length !== 6) {
    errorMessage.value = 'Please enter a valid 6-digit verification code'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const response = await $fetch('/api/auth/verify-otp', {
      method: 'POST',
      body: {
        email: email.value,
        token: otp.value
      }
    })

    if (response.success && response.user) {
      // 设置用户状态
      user.value = response.user
      cookieUtil.setCookie('xToken', response.user.id, {
        expires: 7,
        path: '/'
      })

      // 更新全局状态
      if (userState) {
        userState.isLoggedIn = true
        userState.credits = response.user.credits || 3
        userState.userId = response.user.id
        localStorage.setItem('userState', JSON.stringify(userState))
      }

      // 等待状态更新完成
      await nextTick()
      // 强制刷新用户数据
      await fetchUserData()

      // 导航到仪表板
      try {
        const currentLang = locale.value
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        await navigateTo(dashboardPath, { replace: true })
      } catch (navError) {
        console.error('Navigation error:', navError)
        window.location.href = localePath('/dashboard')
      }
    }
  } catch (error: any) {
    console.error('Verify OTP error:', error)
    errorMessage.value = error.data?.message || 'Invalid or expired verification code. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// 重置验证码状态
const resetOTP = () => {
  otpSent.value = false
  otp.value = ''
  errorMessage.value = ''
  resendCooldown.value = 0
}

// 开始重发倒计时
const startResendCooldown = () => {
  resendCooldown.value = 60
  const timer = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 处理Google登录回调
const handleCredentialResponse = async (response: any) => {
  if (loginMethod.value !== 'google') return

  isLoading.value = true
  errorMessage.value = ''

  try {
    const res = await fetch(`${config.public.apiBase}/api/auth/google-login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ credential: response.credential }),
    })
    const data = await res.json()

    if (data.success) {
      user.value = data.user
      cookieUtil.setCookie('xToken', data.user.id, {
        expires: 7,
        path: '/'
      })

      // 更新全局状态
      if (userState) {
        userState.isLoggedIn = true
        userState.credits = data.user.credits || 3
        userState.userId = data.user.id
        localStorage.setItem('userState', JSON.stringify(userState))
      }

      // 等待状态更新完成
      await nextTick()
      // 强制刷新用户数据
      await fetchUserData()

      // 使用 try-catch 确保导航成功
      try {
        const currentLang = locale.value
        const dashboardPath = currentLang === 'en' ? '/dashboard' : `/${currentLang}/dashboard`
        await navigateTo(dashboardPath, { replace: true })
      } catch (navError) {
        console.error('Navigation error:', navError)
        // 如果导航失败，使用 window.location
        window.location.href = localePath('/dashboard')
      }
    }
  } catch (error) {
    console.error('Google login error:', error)
    errorMessage.value = 'Google login failed. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// 确保在使用前检查 googleClientId
if (!googleClientId && process.dev) {
  console.error('Google Client ID is not configured')
}

// 初始化 Google 登录按钮
const initGoogleButton = () => {
  if (!window.google || !isGoogleScriptLoaded.value || loginMethod.value !== 'google') return

  const button = document.getElementById('googleSignInButton')
  if (!button) return

  window.google.accounts.id.initialize({
    client_id: googleClientId,
    callback: handleCredentialResponse,
    ux_mode: 'popup'
  })

  window.google.accounts.id.renderButton(button, {
    type: 'standard',
    theme: 'outline',
    size: 'large',
    width: 320  // 设置固定宽度
  })
}

// 监听登录方法变化，重新初始化Google按钮
watch(loginMethod, (newMethod) => {
  if (newMethod === 'google') {
    nextTick(() => {
      initGoogleButton()
    })
  }
  // 切换登录方法时清除错误信息
  errorMessage.value = ''
})

// 加载 Google 登录脚本并初始化按钮
onMounted(() => {
  if (googleClientId) {
    const script = document.createElement('script')
    script.src = 'https://accounts.google.com/gsi/client'
    script.onload = () => {
      isGoogleScriptLoaded.value = true
      if (loginMethod.value === 'google') {
        initGoogleButton()
      }
    }
    document.head.appendChild(script)
  }
})

// SEO
useHead({
  title: 'Sign In - Fillify',
  meta: [
    { name: 'description', content: 'Sign in to Fillify to access your AI-powered form filling assistant' },
  ],
})
</script>