export default {
  $locale: {
    name: 'English',
    nativeName: 'English'
  },
  welcome: 'Welcome to Fillify',
  description: 'AI-Powered Form Filling',
  nav: {
    home: 'Home',
    blog: 'Blog',
    signin: 'Sign in',
    dashboard: 'Dashboard',
    signout: 'Sign out',
    startFree: 'Start Free',
    language: 'Language'
  },
  hero: {
    chromeStore: 'Now Available on Chrome Web Store',
    title: {
      text: 'Transform Your Forms Filling with ',
      rotatingWords: {
        0: 'AI-Powered Magic',
        1: 'Smart Automation',
        2: 'Future Technology',
        3: 'Perfect Precision',
        4: 'Seamless Integration'
      }
    },
    description: 'Just type one sentence, and AI will instantly fill any web form. The smartest way to handle online forms.',
    cta: {
      chrome: 'Add to Chrome',
      learnMore: 'Learn More'
    }
  },
  stats: {
    forms: {
      value: '+',
      label: 'Forms Filled Daily'
    },
    accuracy: {
      value: '%',
      label: 'Accuracy Rate'
    },
    support: {
      value: '24/7',
      label: 'AI Support'
    }
  },
  features: {
    title: 'Experience the Power of AI',
    subtitle: 'Discover how Fillify transforms your daily workflow with intelligent automation',
    formFilling: {
      title: 'Universal Form Filling',
      description: 'Fill any web form with AI precision. From simple registration forms to complex applications, Fillify transforms your text descriptions into accurate form data, saving you hours of manual work.',
      alt: 'Screenshot showing Fillify automatically filling a web form with AI assistance'
    },
    email: {
      title: 'Smart Email Assistant',
      description: 'Instantly compose professional emails with our AI-powered assistant. Supporting Gmail and Outlook, it converts your brief descriptions into well-structured emails, making email writing effortless.',
      alt: 'Demonstration of Fillify composing an email with AI suggestions'
    },
    bugReport: {
      title: 'Intelligent Bug Reports',
      description: 'Generate comprehensive bug reports with a single click. Supporting GitHub Issues, JIRA, and other platforms, our AI transforms your brief descriptions into detailed, well-structured bug reports that help your team communicate effectively.',
      alt: 'Example of Fillify generating a detailed bug report for GitHub Issues'
    },
    aiProvider: {
      title: 'Choose Your AI Provider',
      description: 'Freedom to choose your preferred AI service. Whether it\'s OpenAI, Anthropic Claude, or others - simply plug in your API key and start using your favorite AI model. Full control over your AI experience with easy provider switching.',
      alt: 'Interface showing different AI provider options in Fillify'
    }
  },
  faq: {
    title: 'Frequently Asked Questions',
    items: {
      what: {
        question: 'What is Fillify?',
        answer: 'Fillify is an AI-powered Chrome extension that fills web forms instantly based on a single sentence. Whether it\'s sign-up forms, bug reports, or emails, Fillify understands your intent and completes forms accurately and intelligently.'
      },
      types: {
        question: 'What types of forms can Fillify handle?',
        answer: 'Fillify works with various forms, including general web forms, bug reports, and emails. It supports text fields, text areas, and more, ensuring seamless automation across different websites.'
      },
      providers: {
        question: 'Which AI providers does Fillify support?',
        answer: 'Fillify integrates with multiple AI providers, including OpenAI, Anthropic Claude, Google Gemini, and Moonshot AI. You can easily switch between providers and use your own API keys for maximum flexibility and control.'
      },
      privacy: {
        question: 'How does Fillify protect my data and privacy?',
        answer: "We take your privacy and data security very seriously. Your API key is stored locally in your browser and only transmitted to our backend server when making a request, where it is encrypted before being forwarded to your chosen AI provider. We do not store, misuse, or share your API key with third parties, ensuring that your data remains safe and secure."
      },
      customize: {
        question: 'Can I customize AI responses for specific forms?',
        answer: 'Yes! In bug report mode, you can create custom templates with predefined information to help generate more accurate and consistent bug reports.'
      },
      languages: {
        question: 'What languages does Fillify support?',
        answer: 'Fillify supports multiple languages and can automatically detect the form\'s language. You can also manually select your preferred output language in the extension popup.'
      }
    }
  },
  bottomCta: {
    subtitle: 'Ready to Transform Your Workflow?',
    title: 'Experience the Future of Form Filling Today',
    button: 'Install Now'
  },
  footer: {
    copyright: '© {year} Fillify. All rights reserved.',
    social: {
      twitter: 'X (Twitter)',
      youtube: 'YouTube'
    },
    links: {
      terms: 'Terms of Service',
      privacy: 'Privacy Policy'
    }
  },
  signin: {
    title: 'Welcome to Fillify',
    subtitle: 'Sign in to use our AI-powered form filling extension',
    features: {
      title: 'What you\'ll get:',
      list: {
        autoFill: 'AI-Powered Form Auto-Fill',
        api: 'Customize with Your Own API',
        early: 'Early Access to New Features'
      }
    },
    terms: {
      prefix: 'By signing in, you agree to our',
      and: 'and'
    },
    seo: {
      title: 'Sign in - Fillify',
      description: 'Sign in to Fillify to access AI-powered form filling features'
    }
  },
  meta: {
    title: 'Fillify - AI-Powered Forms, Emails & Bug Reports Assistant',
    description: 'Fillify revolutionizes form filling with AI technology. Automatically complete web forms, compose emails, and generate bug reports with intelligent automation.',
    keywords: {
      formFilling: 'AI Form Filler',
      automation: 'AI Automation',
      email: 'AI Email Generation',
      bugReport: 'AI Bug Report Generation',
      additional: [
        'Smart Form Completion',
        'Automated Data Entry',
        'AI Form Assistant',
        'Intelligent Form Filling',
        'Chrome Form Autofill',
        'AI Form Filler'
      ]
    }
  },
  privacy: {
    meta: {
      title: 'Privacy Policy - Fillify',
      description: 'Learn about how Fillify protects your privacy and handles your data.'
    },
    title: 'Privacy Policy',
    lastUpdated: 'Last updated: {date}'
  },
  terms: {
    meta: {
      title: 'Terms of Service - Fillify',
      description: 'Read about the terms and conditions for using Fillify services.'
    },
    title: 'Terms of Service',
    lastUpdated: 'Last updated: {date}'
  },
  dashboard: {
    meta: {
      title: 'Dashboard - Fillify',
      description: 'Manage your Fillify account, view your current plan, and track usage.'
    },
    currentPlan: 'Current Plan',
    settings: 'Settings',
    usageOverview: 'Usage Overview',
    creditsUsed: 'Credits Used'
  },
  blog: {
    meta: {
      title: 'Blog - Fillify',
      description: 'Read the latest news, updates, and tips about AI-powered form filling and productivity automation.'
    },
    hero: {
      badge: 'Latest Updates',
      title: 'Blog',
      subtitle: 'Latest news, releases, and tips'
    },
    list: {
      readMore: 'Read More',
      publishedOn: 'Published on',
      minRead: 'min read',
      noPostsTitle: 'No posts yet',
      noPostsDescription: 'We\'re working on creating great content. Please check back soon.'
    },
    article: {
      backToBlog: 'Back to Blog',
      thanksTitle: 'Thanks for reading!',
      thanksDescription: 'If you have any questions or suggestions about Fillify, feel free to contact us.',
      tryFillify: 'Try Fillify',
      moreArticles: 'More Articles',
      notFoundTitle: 'Article Not Found',
      notFoundDescription: 'Sorry, the article you\'re looking for doesn\'t exist or has been removed.',
      backToBlogBtn: 'Back to Blog'
    }
  },
  demo: {
    meta: {
      title: 'Demo Form - Test Fillify Auto-Fill',
      description: 'Try out Fillify\'s AI-powered form filling capabilities with this interactive demo form. See how AI can transform your form filling experience.'
    }
  },
  '404': {
    title: 'Page Not Found',
    description: 'Sorry, we couldn\'t find the page you\'re looking for. Please check the URL or return to the homepage.',
    backHome: 'Back to Home'
  }
} 